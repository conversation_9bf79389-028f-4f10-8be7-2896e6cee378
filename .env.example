# German Daily Word Bot - Environment Variables Template
# Copy this file to .env and fill in your actual values

# REQUIRED: Your Telegram Bot Token from @BotFather
# Get this by messaging @BotFather on Telegram and creating a new bot
BOT_TOKEN=your_telegram_bot_token_here

# REQUIRED: Your Telegram Chat ID
# Get this by messaging @userinfobot on Telegram
CHAT_ID=your_telegram_chat_id_here

# OPTIONAL: Number of words to send per day (3-5 recommended)
WORDS_PER_DAY=3

# OPTIONAL: Port for webhook server (default: 5000)
PORT=5000

# OPTIONAL: Environment (development/production)
ENVIRONMENT=development

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Edit .env with your actual values
# 3. Never commit .env to git (it's in .gitignore)
# 4. For deployment, set these as environment variables on your platform
