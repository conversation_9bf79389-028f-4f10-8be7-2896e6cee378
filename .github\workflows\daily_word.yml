name: German Learning System

on:
  schedule:
    - cron: '0 9 * * *'
    - cron: '0 19 * * 2,4,6'
    - cron: '0 20 * * 0'

  workflow_dispatch:
    inputs:
      action:
        description: 'Action to perform'
        required: true
        default: 'daily_lesson'
        type: choice
        options:
          - 'daily_lesson'
          - 'quiz'
          - 'weekly_report'
          - 'all'
      words_per_day:
        description: 'Number of words to send (3-5)'
        required: false
        default: '3'
        type: choice
        options:
          - '3'
          - '4'
          - '5'

jobs:
  determine-action:
    runs-on: ubuntu-latest
    outputs:
      action: ${{ steps.determine.outputs.action }}
    steps:
      - name: Determine action based on schedule or input
        id: determine
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "action=${{ github.event.inputs.action }}" >> $GITHUB_OUTPUT
          else
            echo "action=daily_lesson" >> $GITHUB_OUTPUT
          fi

  german-learning-system:
    needs: determine-action
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Handle new user registrations
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        run: |
          echo "👥 Processing new user registrations..."
          python handle_new_users.py

      - name: Send daily German lessons to all users
        if: needs.determine-action.outputs.action == 'daily_lesson' || needs.determine-action.outputs.action == 'all'
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
          WORDS_PER_DAY: ${{ github.event.inputs.words_per_day || '3' }}
        run: |
          echo "🇩🇪 Sending daily German lessons to all registered users..."
          echo "Words per day: $WORDS_PER_DAY"
          python multi_user_bot.py

      - name: Send vocabulary quizzes to all users
        if: needs.determine-action.outputs.action == 'quiz' || needs.determine-action.outputs.action == 'all'
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        run: |
          echo "🧠 Sending vocabulary quizzes to all registered users..."
          python multi_user_quiz.py

      - name: Send weekly reports to all users
        if: needs.determine-action.outputs.action == 'weekly_report' || needs.determine-action.outputs.action == 'all'
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        run: |
          echo "📊 Sending weekly progress reports to all registered users..."
          python multi_user_reports.py

      - name: Upload logs and progress files
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: learning-system-logs
          path: |
            *.log
            progress_*.json
            quiz_*.json
          retention-days: 30
          if-no-files-found: warn
